package service

import (
	"context"
	"errors"
	"strconv"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/cache"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// CreatePost 创建帖子
func (s *Service) CreatePost(req *define.CreatePostReq) (*define.CreatePostResp, error) {
	userID := s.userService.GetUserId()

	// 验证帖子参数
	if err := logic.ValidatePostParams(req.Description, req.Price); err != nil {
		return nil, err
	}

	// 检查用户是否为商家
	if !logic.CheckUserMerchantStatus(s.ctx, userID) {
		log.Ctx(s.ctx).Warnf("非商家用户 %s 尝试创建帖子", userID)
		return nil, define.CC500003Err.SetMsg("只有商家才能发布帖子")
	}

	// 生成帖子ID
	postID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)

	// 创建帖子模型
	post := &model.Post{
		ID:          postID,
		MerchantID:  userID,
		Description: req.Description,
		Price:       req.Price,                     // 直接存储分值
		Status:      int32(enums.PostStatusActive), // 默认为上架状态
	}

	// 设置媒体文件
	if err := post.SetMediaFiles(req.MediaFiles); err != nil {
		log.Ctx(s.ctx).Errorf("设置媒体文件失败: %v", err)
		return nil, define.CC500007Err
	}

	// 保存到数据库
	postSchema := repo.GetQuery().Post
	err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).Save(post)
	if err != nil {
		log.Ctx(s.ctx).Errorf("创建帖子失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 同步清理缓存（新帖子创建会影响列表缓存）
	if err := logic.InvalidatePostCache(s.ctx, postID); err != nil {
		log.Ctx(s.ctx).Warnf("清理帖子缓存失败: %v", err)
	}

	return &define.CreatePostResp{
		ID: postID,
	}, nil
}

// GetPostDetail 获取帖子详情
func (s *Service) GetPostDetail(req *define.GetPostDetailReq) (*define.GetPostDetailResp, error) {
	// 获取当前用户ID（可能为空，表示未登录用户）
	currentUserID := s.userService.GetUserId()

	// 先尝试从缓存获取（只有已上架的帖子才会被缓存）
	// 对于已登录用户，如果缓存命中且是已上架状态，也可以直接返回
	postDetail, err := logic.GetPostDetailFromCache(s.ctx, req.ID)
	if err != nil {
		// 检查是否为空值缓存（数据确实不存在）
		if errors.Is(err, cache.ErrCacheDataNotFound) {
			return nil, define.CC500001Err // 直接返回"帖子不存在"错误，不查询数据库
		}
		log.Ctx(s.ctx).Errorf("从缓存获取帖子详情失败: %v", err)
		// 其他缓存错误时降级到数据库查询
	} else if postDetail != nil {
		// 缓存中的帖子一定是已上架状态，所有人都可以查看
		return postDetail, nil
	}

	// 缓存未命中或失败，从数据库查询
	postSchema := repo.GetQuery().Post
	queryWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, req.ID).
		Build()

	post, err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 设置空值缓存，防止缓存穿透
			cacheKey := constant.GetPostDetailKey(req.ID)
			if cacheErr := cache.SetNullCache(s.ctx, cacheKey); cacheErr != nil {
				log.Ctx(s.ctx).Warnf("设置帖子详情空值缓存失败: %v", cacheErr)
			}
			return nil, define.CC500001Err
		}
		log.Ctx(s.ctx).Errorf("查询帖子详情失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 权限检查：根据帖子状态和用户身份判断是否有权限查看
	postStatus := post.GetStatus()
	switch postStatus {
	case enums.PostStatusActive:
		// 已上架：所有人可见
		break
	case enums.PostStatusInactive, enums.PostStatusViolation:
		// 已下架、违规下架：仅帖子所有者可见
		if currentUserID == "" || currentUserID != post.MerchantID {
			return nil, define.CC500009Err // 帖子已下架
		}
	case enums.PostStatusDeleted:
		// 已删除：所有人不可见
		return nil, define.CC500001Err // 帖子不存在
	default:
		// 未知状态
		return nil, define.CC500001Err // 帖子不存在
	}

	// 获取媒体文件
	mediaFiles, err := post.GetMediaFiles()
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取媒体文件失败: %v", err)
		mediaFiles = []define.MediaFile{}
	}

	// 获取商家信息（名称、头像）
	userInfo, err := facade.GetNodeUser(s.ctx, post.MerchantID)
	if err != nil {
		return nil, err
	}
	merchantInfo := define.UserInfo{
		ID:     post.MerchantID,
		Name:   userInfo.PatbgDetail.Nickname,
		Avatar: userInfo.PatbgDetail.Avatar,
	}

	result := &define.GetPostDetailResp{
		ID:           post.ID,
		MerchantID:   post.MerchantID,
		MerchantInfo: merchantInfo,
		Description:  post.Description,
		Price:        post.Price,
		MediaFiles:   mediaFiles,
		Status:       post.GetStatus(),
		CreatedAt:    post.CreatedAt,
		UpdatedAt:    post.UpdatedAt,
	}

	// 设置缓存（只缓存已上架的帖子）
	if postStatus == enums.PostStatusActive {
		if err := logic.SetPostDetailCache(s.ctx, req.ID, result); err != nil {
			log.Ctx(s.ctx).Warnf("设置帖子详情缓存失败: %v", err)
		}
	}

	return result, nil
}

// GetPostList 获取帖子列表
func (s *Service) GetPostList(req *define.GetPostListReq) (*define.GetPostListResp, error) {
	// 先尝试从缓存获取（只缓存第一页，因为大部分用户只看第一页）
	postList, err := logic.GetPostListFromCache(s.ctx, req.GetPage(), req.GetPageSize())
	if err != nil {
		log.Ctx(s.ctx).Errorf("从缓存获取帖子列表失败: %v", err)
		// 缓存失败时降级到数据库查询
	} else if postList != nil {
		return postList, nil
	}

	// 缓存未命中或失败，从数据库查询
	postSchema := repo.GetQuery().Post
	queryBuilder := search.NewQueryBuilder().
		Eq(postSchema.Status, int32(enums.PostStatusActive)) // 只查询已上架的帖子

	// 默认按发布时间倒序排列
	queryBuilder.OrderByDesc(postSchema.CreatedAt)

	// 查询帖子列表
	queryWrapper := queryBuilder.Build()

	// 添加分页条件
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakePaginate(req.GetPageSize(), req.GetPage()))

	posts, err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).SelectList(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询帖子列表失败: %v", err)
		return nil, commondefine.CommonErr
	}

	merchantIDs := make([]string, 0, len(posts))
	for _, post := range posts {
		merchantIDs = append(merchantIDs, post.MerchantID)
	}
	userMap, err := facade.GetNodeUserMap(s.ctx, merchantIDs)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	list := make([]*define.GetPostListData, 0, len(posts))
	for _, post := range posts {
		mediaFiles, err := post.GetMediaFiles()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取媒体文件失败: %v", err)
			mediaFiles = []define.MediaFile{}
		}

		user := userMap[post.MerchantID]
		if user == nil {
			log.Ctx(s.ctx).Errorf("获取用户信息失败: %s", post.MerchantID)
			return nil, define.CC500105Err
		}
		merchantInfo := define.UserInfo{
			ID:     post.MerchantID,
			Name:   user.PatbgDetail.Nickname,
			Avatar: user.PatbgDetail.Avatar,
		}

		list = append(list, &define.GetPostListData{
			ID:           post.ID,
			MerchantID:   post.MerchantID,
			MerchantInfo: merchantInfo,
			Description:  post.Description,
			Price:        post.Price,
			MediaFiles:   mediaFiles,
			CreatedAt:    post.CreatedAt,
		})
	}

	// 计算是否有更多数据（用户端分页使用HasMore格式）
	hasMore := len(list) == req.GetPageSize()

	result := &define.GetPostListResp{
		List:    list,
		HasMore: hasMore,
	}

	// 异步设置缓存（只缓存第一页）
	go func() {
		// 使用独立的上下文和超时控制，避免请求上下文取消导致缓存设置失败
		cacheCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		if err := logic.SetPostListCache(cacheCtx, req.GetPage(), req.GetPageSize(), result); err != nil {
			log.Ctx(cacheCtx).Warnf("设置帖子列表缓存失败: %v", err)
		}
	}()

	return result, nil
}

// GetMyPosts 获取我的帖子列表
func (s *Service) GetMyPosts(req *define.GetMyPostListReq) (*define.GetMyPostListResp, error) {
	userID := s.userService.GetUserId()

	postSchema := repo.GetQuery().Post
	queryBuilder := search.NewQueryBuilder().
		Eq(postSchema.MerchantID, userID)

	// 状态筛选
	if req.Status != 0 {
		// 当筛选下架状态时，同时包含下架(-2)和违规下架(-3)
		if req.Status == enums.PostStatusInactive {
			queryBuilder.In(postSchema.Status, []int32{int32(enums.PostStatusInactive), int32(enums.PostStatusViolation)})
		} else {
			queryBuilder.Eq(postSchema.Status, int32(req.Status))
		}
	} else {
		// 默认不显示已删除的帖子
		queryBuilder.Ne(postSchema.Status, int32(enums.PostStatusDeleted))
	}

	// 按创建时间倒序排列
	queryBuilder.OrderByDesc(postSchema.CreatedAt)

	// 查询我的帖子列表
	queryWrapper := queryBuilder.Build()

	// 添加分页条件
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakePaginate(req.GetPageSize(), req.GetPage()))

	posts, err := repo.NewPostRepo(postSchema.WithContext(s.ctx)).SelectList(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询我的帖子列表失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 转换为响应格式
	list := make([]*define.GetMyPostListData, 0, len(posts))
	for _, post := range posts {
		mediaFiles, err := post.GetMediaFiles()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取媒体文件失败: %v", err)
			mediaFiles = []define.MediaFile{}
		}

		list = append(list, &define.GetMyPostListData{
			ID:          post.ID,
			Description: post.Description,
			Price:       post.Price,
			MediaFiles:  mediaFiles,
			Status:      post.GetStatus(),
			CreatedAt:   post.CreatedAt,
		})
	}

	// 计算是否有更多数据
	hasMore := len(list) == req.GetPageSize()

	return &define.GetMyPostListResp{
		List:    list,
		HasMore: hasMore,
	}, nil
}

// EditPost 编辑帖子内容
func (s *Service) EditPost(req *define.EditPostReq) (*define.EditPostResp, error) {
	userID := s.userService.GetUserId()

	// 验证帖子参数
	if err := logic.ValidatePostParams(req.Description, req.Price); err != nil {
		return nil, err
	}

	// 检查用户是否为商家
	if !logic.CheckUserMerchantStatus(s.ctx, userID) {
		log.Ctx(s.ctx).Warnf("非商家用户 %s 尝试编辑帖子", userID)
		return nil, define.CC500003Err.SetMsg("只有商家才能编辑帖子")
	}

	// 获取用户帖子（验证所有权）
	post, err := logic.GetUserPostByID(s.ctx, userID, req.ID)
	if err != nil {
		return nil, err
	}

	// 验证帖子是否可编辑
	if post.GetStatus() == enums.PostStatusViolation {
		return nil, define.CC500008Err.SetMsg("违规下架的帖子不能编辑")
	}
	if post.GetStatus() == enums.PostStatusDeleted {
		return nil, define.CC500008Err.SetMsg("已删除的帖子不能编辑")
	}

	// 更新帖子内容
	post.Description = req.Description
	post.Price = req.Price

	// 设置媒体文件
	if err := post.SetMediaFiles(req.MediaFiles); err != nil {
		log.Ctx(s.ctx).Errorf("设置媒体文件失败: %v", err)
		return nil, define.CC500007Err
	}

	// 保存到数据库
	postSchema := repo.GetQuery().Post
	err = repo.NewPostRepo(postSchema.WithContext(s.ctx)).UpdateById(post)
	if err != nil {
		log.Ctx(s.ctx).Errorf("编辑帖子失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 清理缓存（帖子内容变更会影响缓存）
	if err := logic.InvalidatePostCache(s.ctx, req.ID); err != nil {
		log.Ctx(s.ctx).Warnf("清理帖子缓存失败: %v", err)
	}

	return &define.EditPostResp{
		ID: req.ID,
	}, nil
}

// UpdatePostStatus 更新帖子状态
func (s *Service) UpdatePostStatus(req *define.UpdatePostStatusReq) (*define.UpdatePostStatusResp, error) {
	userID := s.userService.GetUserId()

	// 检查用户是否为商家
	if !logic.CheckUserMerchantStatus(s.ctx, userID) {
		log.Ctx(s.ctx).Warnf("非商家用户 %s 尝试更新帖子状态", userID)
		return nil, define.CC500003Err.SetMsg("只有商家才能更新帖子状态")
	}

	// 获取用户帖子（验证所有权）
	post, err := logic.GetUserPostByID(s.ctx, userID, req.ID)
	if err != nil {
		return nil, err
	}

	// 验证帖子是否可上下架
	if post.GetStatus() == enums.PostStatusViolation {
		return nil, define.CC500010Err.SetMsg("违规下架的帖子不能上下架")
	}
	if post.GetStatus() == enums.PostStatusDeleted {
		return nil, define.CC500010Err.SetMsg("已删除的帖子不能上下架")
	}

	// 检查状态是否有效
	if !req.Status.IsValid() {
		return nil, define.CC500002Err
	}

	// 用户不允许将自己的帖子状态变为违规下架
	if req.Status == enums.PostStatusViolation {
		return nil, define.CC500003Err.SetMsg("用户不能将帖子状态设为违规下架")
	}

	// 更新状态
	postSchema := repo.GetQuery().Post
	updateData := map[string]any{
		"status": int32(req.Status),
	}

	updateWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, req.ID).
		Eq(postSchema.MerchantID, userID).
		Build()

	err = repo.NewPostRepo(postSchema.WithContext(s.ctx)).UpdateField(updateData, updateWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("更新帖子状态失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 清理缓存（确保状态更新后立即清理缓存）
	if err := logic.InvalidatePostCache(s.ctx, req.ID); err != nil {
		log.Ctx(s.ctx).Warnf("清理帖子缓存失败: %v", err)
	}

	return &define.UpdatePostStatusResp{
		ID: req.ID,
	}, nil
}

// DeletePost 删除帖子
func (s *Service) DeletePost(req *define.DeletePostReq) (*define.DeletePostResp, error) {
	userID := s.userService.GetUserId()

	// 检查用户是否为商家
	if !logic.CheckUserMerchantStatus(s.ctx, userID) {
		log.Ctx(s.ctx).Warnf("非商家用户 %s 尝试删除帖子", userID)
		return nil, define.CC500003Err.SetMsg("只有商家才能删除帖子")
	}

	// 获取用户帖子（验证所有权）
	_, err := logic.GetUserPostByID(s.ctx, userID, req.ID)
	if err != nil {
		return nil, err
	}

	// 更新状态为删除
	postSchema := repo.GetQuery().Post
	updateData := map[string]any{
		"status": int32(enums.PostStatusDeleted),
	}

	updateWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, req.ID).
		Eq(postSchema.MerchantID, userID).
		Build()

	err = repo.NewPostRepo(postSchema.WithContext(s.ctx)).UpdateField(updateData, updateWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("删除帖子失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 清理缓存（确保状态更新后立即清理缓存）
	if err := logic.InvalidatePostCache(s.ctx, req.ID); err != nil {
		log.Ctx(s.ctx).Warnf("清理帖子缓存失败: %v", err)
	}

	return &define.DeletePostResp{
		ID: req.ID,
	}, nil
}
