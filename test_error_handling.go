package main

import (
	"errors"
	"fmt"

	"app_service/pkg/cache"
)

// 测试优雅的错误处理
func main() {
	
	fmt.Println("=== 测试优雅的错误处理 ===")
	
	// 测试1：模拟空值缓存错误
	fmt.Println("\n1. 测试空值缓存错误处理:")
	err := cache.ErrCacheDataNotFound
	
	// 旧的不优雅方式（已修复）
	// if err.Error() == "数据不存在" {
	//     fmt.Println("❌ 不优雅：使用字符串比较检测错误")
	// }
	
	// 新的优雅方式
	if errors.Is(err, cache.ErrCacheDataNotFound) {
		fmt.Println("✅ 优雅：使用 errors.Is() 检测特定错误类型")
	}
	
	// 测试2：错误类型检查
	fmt.Println("\n2. 测试错误类型检查:")
	testError := cache.ErrCacheDataNotFound
	
	if errors.Is(testError, cache.ErrCacheDataNotFound) {
		fmt.Println("✅ 正确识别缓存数据不存在错误")
	} else {
		fmt.Println("❌ 错误识别失败")
	}
	
	// 测试3：不同错误的区分
	fmt.Println("\n3. 测试不同错误的区分:")
	otherError := errors.New("其他错误")
	
	if errors.Is(otherError, cache.ErrCacheDataNotFound) {
		fmt.Println("❌ 错误地识别为缓存数据不存在错误")
	} else {
		fmt.Println("✅ 正确区分不同类型的错误")
	}
	
	fmt.Println("\n=== 错误处理优化完成 ===")
	fmt.Println("优化内容:")
	fmt.Println("1. 在 pkg/cache/redis.go 中定义了专用的错误变量 ErrCacheDataNotFound")
	fmt.Println("2. 将 GetCache 函数中的字符串错误改为返回专用错误变量")
	fmt.Println("3. 在 post_web.go 中使用 errors.Is() 替代字符串比较")
	fmt.Println("4. 提高了代码的可维护性和类型安全性")
}
